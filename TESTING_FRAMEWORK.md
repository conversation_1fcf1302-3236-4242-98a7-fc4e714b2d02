# AltDVB Interface Testing Framework and Debugging Guide

## Overview
This document provides comprehensive testing procedures and debugging strategies for AltDVB v2.3 NonBDA interface modules.

## Testing Environment Setup

### Prerequisites
- Windows test machine (preferably Windows 7 for best compatibility)
- AltDVB v2.3 installation
- Target DVB hardware devices
- Network connection for downloading test streams
- Signal generator or known good satellite/terrestrial signal source

### Test Environment Configuration
```
Test_Environment/
├── AltDVB_v2.3/
│   ├── AltDVB.exe
│   ├── Interfaces/
│   │   └── [Interface modules under test]
│   └── Logs/
│       └── [Test logs and debug output]
├── Test_Tools/
│   ├── DVB_Analyzer.exe
│   ├── Signal_Monitor.exe
│   └── Log_Parser.exe
└── Test_Data/
    ├── Known_Good_Channels.txt
    ├── Test_Streams/
    └── Reference_Signals/
```

## Testing Methodology

### 1. Module Loading Tests

#### Test Case: Interface Module Loading
```
Test ID: TF-001
Purpose: Verify interface module loads correctly
Steps:
1. Place interface module in AltDVB/Interfaces/ directory
2. Start AltDVB application
3. Check application logs for loading confirmation
4. Verify no error messages in Windows Event Log

Expected Result: Module loads without errors
Pass Criteria: No error messages, module appears in device list
```

#### Test Case: Dependency Verification
```
Test ID: TF-002
Purpose: Verify all required dependencies are present
Steps:
1. Check for required DLLs (DwUsbApi.dll, ttlcdacc.dll)
2. Verify driver versions
3. Test with missing dependencies
4. Document error messages

Expected Result: Clear error messages for missing dependencies
Pass Criteria: Graceful failure with informative error messages
```

### 2. Hardware Detection Tests

#### Test Case: Device Enumeration
```
Test ID: TF-003
Purpose: Verify hardware devices are properly detected
Steps:
1. Connect/install target hardware
2. Start AltDVB with interface module
3. Check device enumeration in AltDVB settings
4. Verify device properties are correctly read

Expected Result: Hardware appears in device list with correct properties
Pass Criteria: Device detected, properties match hardware specifications
```

#### Test Case: Multiple Device Support
```
Test ID: TF-004
Purpose: Test support for multiple devices of same type
Steps:
1. Connect multiple compatible devices
2. Start AltDVB
3. Verify all devices are detected
4. Test switching between devices

Expected Result: All devices detected and selectable
Pass Criteria: No conflicts, proper device identification
```

### 3. Signal Acquisition Tests

#### Test Case: Channel Tuning
```
Test ID: TF-005
Purpose: Verify channel tuning functionality
Steps:
1. Configure known good channel parameters
2. Attempt to tune to channel
3. Monitor signal strength and quality
4. Verify lock status

Expected Result: Successful channel lock with good signal metrics
Pass Criteria: Signal lock achieved, stable signal quality
```

#### Test Case: Signal Monitoring
```
Test ID: TF-006
Purpose: Test signal strength and quality reporting
Steps:
1. Tune to various signal strengths
2. Monitor reported signal levels
3. Compare with external signal meter
4. Test signal loss scenarios

Expected Result: Accurate signal reporting
Pass Criteria: Signal levels match external measurements ±5%
```

### 4. DiSEqC Testing (Satellite Devices)

#### Test Case: DiSEqC Commands
```
Test ID: TF-007
Purpose: Verify DiSEqC command functionality
Steps:
1. Configure DiSEqC switch/motor
2. Send various DiSEqC commands
3. Verify switch/motor responds correctly
4. Test command timing and reliability

Expected Result: DiSEqC commands execute correctly
Pass Criteria: 100% command success rate, proper timing
```

### 5. Streaming and Recording Tests

#### Test Case: Live Streaming
```
Test ID: TF-008
Purpose: Test live video/audio streaming
Steps:
1. Tune to channel with known content
2. Start live playback
3. Monitor for dropouts or artifacts
4. Test for extended periods (30+ minutes)

Expected Result: Smooth, continuous playback
Pass Criteria: <0.1% packet loss, no visible artifacts
```

#### Test Case: Recording Functionality
```
Test ID: TF-009
Purpose: Test recording capabilities
Steps:
1. Start recording on tuned channel
2. Record for various durations
3. Verify file integrity
4. Test simultaneous record/playback

Expected Result: Clean recordings without corruption
Pass Criteria: Playable files, no data corruption
```

## Debugging Strategies

### 1. Log Analysis

#### AltDVB Application Logs
```
Location: AltDVB/Logs/
Key Files:
- AltDVB.log (main application log)
- Interface_[ModuleName].log (interface-specific logs)
- Error.log (error messages)

Analysis Tools:
- Text editors with regex search
- Log parsing scripts
- Timeline analysis tools
```

#### Windows Event Logs
```
Locations to Check:
- Windows Logs > Application
- Windows Logs > System
- Applications and Services Logs > Hardware Events

Key Event IDs:
- Driver loading/unloading events
- Hardware detection events
- Error and warning messages
```

### 2. Hardware-Specific Debugging

#### DvbWorld USB Devices
```
Common Issues:
1. DwUsbApi.dll not found
   - Solution: Copy DLL to AltDVB root directory
   - Verify: Check DLL version compatibility

2. USB enumeration failures
   - Solution: Try different USB ports
   - Verify: Check Windows Device Manager

3. Signal acquisition problems
   - Solution: Check antenna connections
   - Verify: Test with manufacturer's software
```

#### SkyStar2 Devices
```
Common Issues:
1. DiSEqC command failures
   - Solution: Update to driver v4.4.0+
   - Verify: Test DiSEqC with external tool

2. Driver compatibility
   - Solution: Use exact driver version
   - Verify: Check driver digital signature

3. PCI slot conflicts
   - Solution: Try different PCI slots
   - Verify: Check IRQ assignments
```

#### TechnoTrend Budget Devices
```
Common Issues:
1. ttlcdacc.dll missing
   - Solution: Copy to ../AltDvb/Devices/
   - Verify: Check DLL version (≥2.19.8.1)

2. WDM driver version mismatch
   - Solution: Install exact version 2.19h
   - Verify: Check driver date (28.11.2006)

3. Card not detected
   - Solution: Reseat PCI card
   - Verify: Check power connections
```

### 3. Performance Debugging

#### CPU Usage Analysis
```
Tools:
- Windows Performance Monitor
- Process Explorer
- AltDVB built-in performance counters

Metrics to Monitor:
- CPU usage during tuning
- Memory consumption
- Thread count and activity
- I/O operations per second
```

#### Memory Leak Detection
```
Tools:
- Application Verifier
- CRT Debug Heap
- VMMap for virtual memory analysis

Test Procedure:
1. Start AltDVB with interface module
2. Perform repetitive operations (tune/untune)
3. Monitor memory usage over time
4. Check for memory growth patterns
```

### 4. Network and Streaming Debugging

#### Packet Loss Analysis
```
Tools:
- Wireshark for network capture
- DVB Analyzer for stream analysis
- Custom packet counting tools

Metrics:
- Continuity counter errors
- Transport stream packet loss
- Buffer underruns/overruns
- Network jitter and latency
```

## Automated Testing Scripts

### PowerShell Test Automation
```powershell
# Example: Automated interface loading test
function Test-InterfaceLoading {
    param([string]$InterfacePath)
    
    # Copy interface to AltDVB directory
    Copy-Item $InterfacePath "C:\AltDVB\Interfaces\"
    
    # Start AltDVB and monitor for errors
    $process = Start-Process "C:\AltDVB\AltDVB.exe" -PassThru
    Start-Sleep 10
    
    # Check for error logs
    $errors = Get-Content "C:\AltDVB\Logs\Error.log" -ErrorAction SilentlyContinue
    
    # Clean up
    Stop-Process $process -Force
    
    return $errors.Count -eq 0
}
```

### Batch Testing Framework
```batch
@echo off
echo Starting AltDVB Interface Test Suite
echo ====================================

call :test_dvbworld
call :test_pinnacle  
call :test_skystar2
call :test_ttbudget

echo.
echo Test Suite Complete
pause
exit /b

:test_dvbworld
echo Testing DvbWorld Interface...
rem Add specific DvbWorld tests here
goto :eof

:test_pinnacle
echo Testing Pinnacle Interface...
rem Add specific Pinnacle tests here
goto :eof
```

## Test Reporting

### Test Report Template
```
Test Report: AltDVB Interface Module Testing
Date: [Date]
Tester: [Name]
Environment: [OS Version, Hardware]

Module Under Test: [Interface Name and Version]

Test Results Summary:
- Total Tests: [Number]
- Passed: [Number]
- Failed: [Number]
- Skipped: [Number]

Detailed Results:
[Test ID] [Test Name] [Result] [Notes]

Issues Found:
[Issue ID] [Severity] [Description] [Workaround]

Recommendations:
[List of recommendations for improvements]
```

### Continuous Integration
```yaml
# Example CI configuration for automated testing
name: AltDVB Interface Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: windows-latest
    steps:
    - uses: actions/checkout@v2
    - name: Setup test environment
      run: |
        # Install AltDVB
        # Copy interface modules
        # Configure test hardware
    - name: Run interface tests
      run: |
        powershell -File "scripts/run-tests.ps1"
    - name: Generate test report
      run: |
        powershell -File "scripts/generate-report.ps1"
```

## Best Practices

### Testing Guidelines
1. **Always test on clean systems** to avoid interference
2. **Use known good hardware** for baseline testing
3. **Document all test conditions** including hardware revisions
4. **Test edge cases** like signal loss, device disconnection
5. **Verify backwards compatibility** with older AltDVB versions

### Debugging Guidelines
1. **Start with simplest test cases** before complex scenarios
2. **Use process of elimination** to isolate issues
3. **Compare with working reference systems** when possible
4. **Document all findings** for future reference
5. **Test fixes thoroughly** before deployment
