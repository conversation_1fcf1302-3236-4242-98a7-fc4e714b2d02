# AltDVB Interface Analysis Tools and Quick Reference

## Quick Reference Summary

### Module Overview

| Module | Version | Hardware | Key Dependencies | Status |
|--------|---------|----------|------------------|--------|
| Dev_DvbWorld.int | v2.3.0.198 | DvbWorld USB | DwUsbApi.dll | Active |
| Dev_Pinnacle.int | v2.3.0.200 | Pinnacle PCTV | DirectShow | Legacy |
| Dev_SkyStar2.int | v2.3.0.770 | SkyStar2 Cards | Driver v4.4.0+ | Active |
| Dev_TTBudget.int | v2.3.0.294 | TT Budget Cards | WDM v2.19h, ttlcdacc.dll | Legacy |

### Critical Dependencies

```text
DvbWorld:    DwUsbApi.dll (in AltDVB root)
TT Budget:   ttlcdacc.dll v********+ (in ../AltDvb/Devices/)
             WDM driver v2.19h (exact version required)
SkyStar2:    Driver v4.4.0+ (for DiSEqC support)
Pinnacle:    Legacy DirectShow components
```

## Analysis Tools and Scripts

### 1. PowerShell Analysis Scripts

#### Module Information Extractor

```powershell
# Get-InterfaceInfo.ps1
function Get-InterfaceInfo {
    param([string]$InterfacePath)
    
    $fileInfo = Get-Item $InterfacePath
    $versionInfo = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($InterfacePath)
    
    Write-Host "Interface Analysis: $($fileInfo.Name)" -ForegroundColor Green
    Write-Host "===========================================" -ForegroundColor Green
    Write-Host "File Size: $($fileInfo.Length) bytes"
    Write-Host "Created: $($fileInfo.CreationTime)"
    Write-Host "Modified: $($fileInfo.LastWriteTime)"
    Write-Host "Version: $($versionInfo.FileVersion)"
    Write-Host "Description: $($versionInfo.FileDescription)"
    
    # Check PE header
    $bytes = [System.IO.File]::ReadAllBytes($InterfacePath)
    if ($bytes[0] -eq 0x4D -and $bytes[1] -eq 0x5A) {
        Write-Host "File Type: Windows PE Executable" -ForegroundColor Yellow
        
        # Check for Delphi/Pascal signatures
        $content = [System.Text.Encoding]::ASCII.GetString($bytes)
        if ($content -match "TObject|String|WideString") {
            Write-Host "Language: Likely Delphi/Pascal" -ForegroundColor Cyan
        }
    }
    
    Write-Host ""
}

# Usage: Get-InterfaceInfo "Dev_DvbWorld.int"
```

#### Dependency Checker

```powershell
# Check-Dependencies.ps1
function Check-Dependencies {
    param([string]$AltDVBPath)
    
    Write-Host "Checking AltDVB Dependencies..." -ForegroundColor Green
    
    # Check for DvbWorld dependencies
    $dwUsbApi = Join-Path $AltDVBPath "DwUsbApi.dll"
    if (Test-Path $dwUsbApi) {
        Write-Host "✓ DwUsbApi.dll found" -ForegroundColor Green
    } else {
        Write-Host "✗ DwUsbApi.dll missing (required for DvbWorld)" -ForegroundColor Red
    }
    
    # Check for TT Budget dependencies
    $ttlcdacc = Join-Path $AltDVBPath "Devices\ttlcdacc.dll"
    if (Test-Path $ttlcdacc) {
        $version = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($ttlcdacc).FileVersion
        Write-Host "✓ ttlcdacc.dll found (version: $version)" -ForegroundColor Green
        
        # Check version requirement (≥********)
        if ([version]$version -ge [version]"********") {
            Write-Host "  ✓ Version meets requirement (≥********)" -ForegroundColor Green
        } else {
            Write-Host "  ✗ Version too old (need ≥********)" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ ttlcdacc.dll missing (required for TT Budget)" -ForegroundColor Red
    }
    
    # Check interface modules
    $interfaces = @("Dev_DvbWorld.int", "Dev_Pinnacle.int", "Dev_SkyStar2.int", "Dev_TTBudget.int")
    $interfacePath = Join-Path $AltDVBPath "Interfaces"
    
    foreach ($interface in $interfaces) {
        $fullPath = Join-Path $interfacePath $interface
        if (Test-Path $fullPath) {
            Write-Host "✓ $interface found" -ForegroundColor Green
        } else {
            Write-Host "✗ $interface missing" -ForegroundColor Yellow
        }
    }
}

# Usage: Check-Dependencies "C:\AltDVB"
```

#### Hardware Detection Script

```powershell
# Detect-DVBHardware.ps1
function Detect-DVBHardware {
    Write-Host "Detecting DVB Hardware..." -ForegroundColor Green
    
    # Check USB devices (for DvbWorld)
    $usbDevices = Get-WmiObject -Class Win32_USBControllerDevice | 
                  ForEach-Object { [wmi]($_.Dependent) } | 
                  Where-Object { $_.Description -like "*DVB*" -or $_.Description -like "*DvbWorld*" }
    
    if ($usbDevices) {
        Write-Host "USB DVB Devices Found:" -ForegroundColor Cyan
        foreach ($device in $usbDevices) {
            Write-Host "  - $($device.Description)" -ForegroundColor White
        }
    }
    
    # Check PCI devices (for SkyStar2, TT Budget)
    $pciDevices = Get-WmiObject -Class Win32_PnPEntity | 
                  Where-Object { $_.Description -like "*DVB*" -or 
                                $_.Description -like "*SkyStar*" -or 
                                $_.Description -like "*TechnoTrend*" -or
                                $_.Description -like "*Budget*" }
    
    if ($pciDevices) {
        Write-Host "PCI DVB Devices Found:" -ForegroundColor Cyan
        foreach ($device in $pciDevices) {
            Write-Host "  - $($device.Description)" -ForegroundColor White
        }
    }
    
    # Check for Pinnacle devices
    $pinnacleDevices = Get-WmiObject -Class Win32_PnPEntity | 
                       Where-Object { $_.Description -like "*Pinnacle*" -or 
                                     $_.Description -like "*PCTV*" }
    
    if ($pinnacleDevices) {
        Write-Host "Pinnacle Devices Found:" -ForegroundColor Cyan
        foreach ($device in $pinnacleDevices) {
            Write-Host "  - $($device.Description)" -ForegroundColor White
        }
    }
    
    if (-not ($usbDevices -or $pciDevices -or $pinnacleDevices)) {
        Write-Host "No DVB hardware detected" -ForegroundColor Yellow
    }
}

# Usage: Detect-DVBHardware
```

### 2. Batch File Utilities

#### Quick Setup Checker

```batch
@echo off
title AltDVB Interface Setup Checker
echo =====================================
echo AltDVB Interface Setup Checker
echo =====================================
echo.

set ALTDVB_PATH=C:\AltDVB
if not exist "%ALTDVB_PATH%" (
    echo ERROR: AltDVB not found at %ALTDVB_PATH%
    pause
    exit /b 1
)

echo Checking AltDVB installation at: %ALTDVB_PATH%
echo.

echo Checking core files...
if exist "%ALTDVB_PATH%\AltDVB.exe" (
    echo [OK] AltDVB.exe found
) else (
    echo [ERROR] AltDVB.exe not found
)

echo.
echo Checking interface modules...
if exist "%ALTDVB_PATH%\Interfaces\Dev_DvbWorld.int" (
    echo [OK] Dev_DvbWorld.int found
) else (
    echo [WARN] Dev_DvbWorld.int not found
)

if exist "%ALTDVB_PATH%\Interfaces\Dev_Pinnacle.int" (
    echo [OK] Dev_Pinnacle.int found
) else (
    echo [WARN] Dev_Pinnacle.int not found
)

if exist "%ALTDVB_PATH%\Interfaces\Dev_SkyStar2.int" (
    echo [OK] Dev_SkyStar2.int found
) else (
    echo [WARN] Dev_SkyStar2.int not found
)

if exist "%ALTDVB_PATH%\Interfaces\Dev_TTBudget.int" (
    echo [OK] Dev_TTBudget.int found
) else (
    echo [WARN] Dev_TTBudget.int not found
)

echo.
echo Checking dependencies...
if exist "%ALTDVB_PATH%\DwUsbApi.dll" (
    echo [OK] DwUsbApi.dll found (DvbWorld support)
) else (
    echo [WARN] DwUsbApi.dll not found (DvbWorld will not work)
)

if exist "%ALTDVB_PATH%\Devices\ttlcdacc.dll" (
    echo [OK] ttlcdacc.dll found (TT Budget support)
) else (
    echo [WARN] ttlcdacc.dll not found (TT Budget will not work)
)

echo.
echo Setup check complete.
pause
```

### 3. Python Analysis Tools

#### Binary Analysis Tool

```python
#!/usr/bin/env python3
# analyze_interface.py
import struct
import sys
import os

def analyze_pe_header(filename):
    """Analyze PE header of interface module"""
    with open(filename, 'rb') as f:
        # Check DOS header
        dos_header = f.read(64)
        if dos_header[:2] != b'MZ':
            print(f"Not a valid PE file: {filename}")
            return
        
        # Get PE header offset
        pe_offset = struct.unpack('<L', dos_header[60:64])[0]
        f.seek(pe_offset)
        
        # Check PE signature
        pe_sig = f.read(4)
        if pe_sig != b'PE\x00\x00':
            print(f"Invalid PE signature: {filename}")
            return
        
        # Read COFF header
        coff_header = f.read(20)
        machine, num_sections, timestamp = struct.unpack('<HHL', coff_header[:8])
        
        print(f"Analysis of {filename}:")
        print(f"  Machine type: 0x{machine:04x}")
        print(f"  Sections: {num_sections}")
        print(f"  Timestamp: {timestamp} ({hex(timestamp)})")
        
        # Read optional header
        opt_header_size = struct.unpack('<H', coff_header[16:18])[0]
        if opt_header_size > 0:
            opt_header = f.read(opt_header_size)
            if len(opt_header) >= 28:
                entry_point = struct.unpack('<L', opt_header[16:20])[0]
                image_base = struct.unpack('<L', opt_header[28:32])[0]
                print(f"  Entry point: 0x{entry_point:08x}")
                print(f"  Image base: 0x{image_base:08x}")

def find_strings(filename, min_length=4):
    """Extract printable strings from binary"""
    strings = []
    with open(filename, 'rb') as f:
        data = f.read()
        current_string = ""
        
        for byte in data:
            if 32 <= byte <= 126:  # Printable ASCII
                current_string += chr(byte)
            else:
                if len(current_string) >= min_length:
                    strings.append(current_string)
                current_string = ""
        
        if len(current_string) >= min_length:
            strings.append(current_string)
    
    return strings

def main():
    if len(sys.argv) != 2:
        print("Usage: python analyze_interface.py <interface_file.int>")
        sys.exit(1)
    
    filename = sys.argv[1]
    if not os.path.exists(filename):
        print(f"File not found: {filename}")
        sys.exit(1)
    
    # Analyze PE structure
    analyze_pe_header(filename)
    
    # Extract interesting strings
    print("\nInteresting strings found:")
    strings = find_strings(filename, 6)
    
    # Filter for relevant strings
    relevant_strings = []
    keywords = ['dvb', 'interface', 'device', 'driver', 'error', 'init', 'tune', 'signal']
    
    for s in strings:
        if any(keyword in s.lower() for keyword in keywords):
            relevant_strings.append(s)
    
    for s in relevant_strings[:20]:  # Show first 20 matches
        print(f"  {s}")
    
    if len(relevant_strings) > 20:
        print(f"  ... and {len(relevant_strings) - 20} more")

if __name__ == "__main__":
    main()
```

### 4. Registry Analysis

#### Registry Keys to Check

```text
Windows Registry Locations for DVB Drivers:

HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Services\
  - Look for DVB-related services
  - Check driver status and configuration

HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\
  - Find installed DVB software and drivers
  - Check version information

HKEY_LOCAL_MACHINE\HARDWARE\DEVICEMAP\
  - Hardware device mappings
  - Driver associations

Device Manager Equivalent Registry:
HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Enum\
  - USB devices: USB\VID_xxxx&PID_xxxx
  - PCI devices: PCI\VEN_xxxx&DEV_xxxx
```

### 5. Log Analysis Tools

#### Log Parser Script

```powershell
# Parse-AltDVBLogs.ps1
function Parse-AltDVBLogs {
    param([string]$LogPath)
    
    $logFiles = Get-ChildItem $LogPath -Filter "*.log"
    
    foreach ($logFile in $logFiles) {
        Write-Host "Analyzing: $($logFile.Name)" -ForegroundColor Green
        
        $content = Get-Content $logFile.FullName
        
        # Count error types
        $errors = $content | Where-Object { $_ -match "ERROR|FAIL|EXCEPTION" }
        $warnings = $content | Where-Object { $_ -match "WARN|WARNING" }
        $info = $content | Where-Object { $_ -match "INFO|SUCCESS" }
        
        Write-Host "  Errors: $($errors.Count)"
        Write-Host "  Warnings: $($warnings.Count)"
        Write-Host "  Info: $($info.Count)"
        
        # Show recent errors
        if ($errors.Count -gt 0) {
            Write-Host "  Recent errors:" -ForegroundColor Red
            $errors | Select-Object -Last 5 | ForEach-Object {
                Write-Host "    $_" -ForegroundColor Red
            }
        }
        
        Write-Host ""
    }
}
```

## Troubleshooting Quick Reference

### Common Issues and Solutions

```text
Issue: "DwUsbApi.dll not found"
Solution: Copy DLL to AltDVB root directory
Command: copy DwUsbApi.dll C:\AltDVB\

Issue: "ttlcdacc.dll version mismatch"
Solution: Install correct version (≥********)
Location: C:\AltDVB\Devices\ttlcdacc.dll

Issue: "SkyStar2 DiSEqC not working"
Solution: Update driver to v4.4.0+
Check: Device Manager > DVB devices

Issue: "Interface module not loading"
Solution: Check Windows Event Log
Command: eventvwr.msc > Windows Logs > Application
```text

### Emergency Recovery

```text
1. Backup current configuration
2. Reinstall interface modules
3. Reset to default settings
4. Test with minimal configuration
5. Gradually add features back
```

This completes the comprehensive analysis and documentation package for the AltDVB interface modules. You now have:

1. **Complete hardware support documentation**
2. **Comprehensive testing framework**
3. **Detailed improvement recommendations**
4. **Practical analysis tools and scripts**

These documents provide everything needed to understand, maintain, test, and improve the AltDVB interface modules.
