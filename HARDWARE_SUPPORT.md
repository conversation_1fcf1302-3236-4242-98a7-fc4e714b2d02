# AltDVB v2.3 NonBDA Interfaces - Hardware Support Documentation

## Overview
This document provides comprehensive information about the DVB hardware devices supported by AltDVB v2.3 NonBDA interfaces and their specific requirements.

## Supported Hardware Modules

### 1. DvbWorld USB Devices (Dev_DvbWorld.int v2.3.0.198)

**Supported Hardware:**
- DvbWorld USB DVB-S devices
- DvbWorld USB DVB-S2 devices
- DvbWorld USB DVB-T devices
- DvbWorld USB DVB-C devices

**Requirements:**
- `DwUsbApi.dll` must be present in the main AltDVB directory
- USB 2.0 port recommended for optimal performance
- Windows drivers for DvbWorld devices must be installed

**Installation Steps:**
1. Install DvbWorld device drivers
2. Copy `DwUsbApi.dll` to AltDVB installation directory
3. Place `Dev_DvbWorld.int` in AltDVB interfaces folder
4. Restart AltDVB application

**Supported Functions:**
- Channel tuning (Satellite, Cable, Terrestrial)
- DiSEqC control for satellite dishes
- Signal strength monitoring
- Remote control support
- Recording capabilities

### 2. Pinnacle PCTV Devices (Dev_Pinnacle.int v2.3.0.200)

**Supported Hardware:**
- Pinnacle PCTV Sat (older models)
- Pinnacle PCTV 400i
- Pinnacle PCTV DVB-T devices
- Legacy Pinnacle DVB-S devices

**Requirements:**
- Pinnacle device drivers (legacy versions)
- DirectShow components
- Windows Media Format SDK (for some models)

**Installation Steps:**
1. Install legacy Pinnacle drivers
2. Ensure DirectShow is properly configured
3. Place `Dev_Pinnacle.int` in AltDVB interfaces folder
4. Configure device settings in AltDVB

**Known Limitations:**
- Primarily supports older Pinnacle hardware
- May require compatibility mode on newer Windows versions
- Limited support for newer DVB-S2 features

### 3. SkyStar2 Devices (Dev_SkyStar2.int v2.3.0.770)

**Supported Hardware:**
- SkyStar2 DVB-S cards
- SkyStar2 Express cards
- Compatible SkyStar2 variants

**Requirements:**
- SkyStar2 drivers version 4.4.0 or higher (recommended)
- PCI or PCIe slot for card installation

**Key Features:**
- Enhanced DiSEqC command support
- Improved driver compatibility (v4.4.0+)
- VCL-free configuration dialog
- Better stability and performance

**Installation Steps:**
1. Install SkyStar2 drivers (v4.4.0 or newer)
2. Insert SkyStar2 card in PCI/PCIe slot
3. Place `Dev_SkyStar2.int` in AltDVB interfaces folder
4. Configure DiSEqC settings if using satellite dish

**DiSEqC Support:**
- DiSEqC 1.0 commands
- DiSEqC 1.1 commands
- Multi-switch support
- Motor control (limited)

### 4. TechnoTrend Budget Devices (Dev_TTBudget.int v2.3.0.294)

**Supported Hardware:**
- TechnoTrend Budget DVB-S cards
- TechnoTrend S2-3200 (tested)
- Compatible TT Budget series cards

**Requirements:**
- WDM driver version 2.19h (dated 28.11.2006) - **CRITICAL**
- `ttlcdacc.dll` version 2.19.8.1 or higher
- PCI slot for card installation

**Installation Steps:**
1. Install WDM driver version 2.19h (specific version required)
2. Copy `ttlcdacc.dll` (v2.19.8.1+) to `..\AltDvb\Devices\` directory
3. Insert TT Budget card in PCI slot
4. Place `Dev_TTBudget.int` in AltDVB interfaces folder
5. Restart system and configure AltDVB

**Critical Notes:**
- **Driver version 2.19h is mandatory** - newer versions may not work
- `ttlcdacc.dll` must be placed in the correct directory
- Tested specifically with S2-3200 model

## General Installation Guidelines

### Directory Structure
```
AltDVB/
├── AltDVB.exe
├── DwUsbApi.dll (for DvbWorld devices)
├── Interfaces/
│   ├── Dev_DvbWorld.int
│   ├── Dev_Pinnacle.int
│   ├── Dev_SkyStar2.int
│   └── Dev_TTBudget.int
└── Devices/
    └── ttlcdacc.dll (for TT Budget devices)
```

### Compatibility Matrix

| Device Type | Windows XP | Windows 7 | Windows 10 | Windows 11 |
|-------------|------------|-----------|------------|------------|
| DvbWorld USB | ✓ | ✓ | ✓* | ✓* |
| Pinnacle PCTV | ✓ | ✓* | ⚠️ | ⚠️ |
| SkyStar2 | ✓ | ✓ | ✓ | ✓ |
| TT Budget | ✓ | ✓ | ⚠️ | ⚠️ |

**Legend:**
- ✓ = Full support
- ✓* = Supported with latest drivers
- ⚠️ = Limited support, may require compatibility mode

### Troubleshooting Common Issues

#### DvbWorld Devices
- **Issue**: Device not detected
- **Solution**: Ensure `DwUsbApi.dll` is in AltDVB root directory

#### Pinnacle Devices  
- **Issue**: No signal on newer Windows
- **Solution**: Run AltDVB in Windows XP compatibility mode

#### SkyStar2 Devices
- **Issue**: DiSEqC commands not working
- **Solution**: Update to driver version 4.4.0 or higher

#### TT Budget Devices
- **Issue**: Card not recognized
- **Solution**: Verify WDM driver is exactly version 2.19h and `ttlcdacc.dll` is in correct location

## Performance Optimization

### USB Devices (DvbWorld)
- Use USB 2.0 ports for better bandwidth
- Avoid USB hubs when possible
- Close unnecessary applications to free USB bandwidth

### PCI/PCIe Cards (SkyStar2, TT Budget)
- Ensure adequate power supply
- Check for IRQ conflicts
- Use dedicated PCI slots when available

## Security Considerations

### DLL Dependencies
- Verify authenticity of required DLLs
- Keep drivers updated from official sources
- Scan for malware regularly

### Driver Security
- Use only official drivers from manufacturers
- Avoid modified or unofficial driver versions
- Keep Windows updated for security patches

## Version History

- **v2.3.0.770** (SkyStar2): Fixed DiSEqC commands, VCL-free dialog
- **v2.3.0.294** (TT Budget): Tested with S2-3200
- **v2.3.0.200** (Pinnacle): Legacy device support
- **v2.3.0.198** (DvbWorld): USB API integration

## Support and Maintenance

### End of Life Notice
These interfaces are part of AltDVB v2.3, which is legacy software. Consider:
- Migrating to newer DVB software for modern hardware
- Using BDA-compliant interfaces when possible
- Keeping backup copies of working driver versions

### Community Resources
- AltDVB user forums
- DVB hardware compatibility databases
- Driver archive repositories
