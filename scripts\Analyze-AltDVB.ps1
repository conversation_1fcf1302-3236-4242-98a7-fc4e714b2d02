# AltDVB Interface Analysis Script
# PowerShell script to analyze AltDVB setup and interface modules

param(
    [string]$AltDVBPath = "C:\AltDVB",
    [string]$InterfacePath = ".",
    [switch]$Detailed,
    [switch]$ExportReport
)

# Color coding for output
$Colors = @{
    Success = "Green"
    Warning = "Yellow" 
    Error = "Red"
    Info = "Cyan"
    Header = "Magenta"
}

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Colors[$Color]
}

function Get-InterfaceModuleInfo {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        return $null
    }
    
    try {
        $fileInfo = Get-Item $FilePath
        $versionInfo = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($FilePath)
        
        # Read first 1KB to analyze header
        $bytes = [System.IO.File]::ReadAllBytes($FilePath) | Select-Object -First 1024
        $content = [System.Text.Encoding]::ASCII.GetString($bytes)
        
        # Detect technology stack
        $technology = "Unknown"
        if ($content -match "TObject|String|WideString|Delphi") {
            $technology = "Delphi/Pascal"
        } elseif ($content -match "MSVC|Visual C\+\+") {
            $technology = "Visual C++"
        } elseif ($content -match "\.NET|mscoree") {
            $technology = ".NET"
        }
        
        # Check for COM/DirectShow interfaces
        $hasDirectShow = $content -match "IBaseFilter|IPin|IMediaSample|DirectShow"
        $hasCOM = $content -match "IUnknown|IInterface|QueryInterface"
        
        return @{
            Name = $fileInfo.Name
            Size = $fileInfo.Length
            Created = $fileInfo.CreationTime
            Modified = $fileInfo.LastWriteTime
            Version = $versionInfo.FileVersion
            Description = $versionInfo.FileDescription
            Technology = $technology
            HasDirectShow = $hasDirectShow
            HasCOM = $hasCOM
            Path = $FilePath
        }
    }
    catch {
        Write-ColorOutput "Error analyzing $FilePath`: $_" "Error"
        return $null
    }
}

function Test-Dependencies {
    param([string]$BasePath)
    
    Write-ColorOutput "`n=== Dependency Analysis ===" "Header"
    
    $dependencies = @{
        "DwUsbApi.dll" = @{
            Path = Join-Path $BasePath "DwUsbApi.dll"
            Required = "DvbWorld interface"
            Critical = $false
        }
        "ttlcdacc.dll" = @{
            Path = Join-Path $BasePath "Devices\ttlcdacc.dll"
            Required = "TechnoTrend Budget interface"
            Critical = $false
            MinVersion = "********"
        }
    }
    
    $results = @()
    
    foreach ($dep in $dependencies.Keys) {
        $info = $dependencies[$dep]
        $exists = Test-Path $info.Path
        
        $result = @{
            Name = $dep
            Exists = $exists
            Required = $info.Required
            Path = $info.Path
        }
        
        if ($exists) {
            try {
                $versionInfo = [System.Diagnostics.FileVersionInfo]::GetVersionInfo($info.Path)
                $result.Version = $versionInfo.FileVersion
                $result.Size = (Get-Item $info.Path).Length
                
                if ($info.MinVersion) {
                    $currentVersion = [version]$versionInfo.FileVersion
                    $minVersion = [version]$info.MinVersion
                    $result.VersionOK = $currentVersion -ge $minVersion
                }
                
                Write-ColorOutput "✓ $dep found (v$($result.Version))" "Success"
                if ($result.VersionOK -eq $false) {
                    Write-ColorOutput "  ⚠ Version too old (need ≥$($info.MinVersion))" "Warning"
                }
            }
            catch {
                Write-ColorOutput "✓ $dep found (version unknown)" "Warning"
                $result.Version = "Unknown"
            }
        }
        else {
            Write-ColorOutput "✗ $dep missing ($($info.Required))" "Error"
        }
        
        $results += $result
    }
    
    return $results
}

function Test-Hardware {
    Write-ColorOutput "`n=== Hardware Detection ===" "Header"
    
    $devices = @()
    
    # USB DVB devices
    try {
        $usbDevices = Get-CimInstance -ClassName Win32_PnPEntity | 
                      Where-Object { $_.Description -like "*DVB*" -or 
                                    $_.Description -like "*DvbWorld*" -or
                                    $_.Description -like "*PCTV*" }
        
        foreach ($device in $usbDevices) {
            Write-ColorOutput "USB: $($device.Description)" "Info"
            $devices += @{
                Type = "USB"
                Description = $device.Description
                Status = $device.Status
                DeviceID = $device.DeviceID
            }
        }
    }
    catch {
        Write-ColorOutput "Error checking USB devices: $_" "Warning"
    }
    
    # PCI DVB devices
    try {
        $pciDevices = Get-CimInstance -ClassName Win32_PnPEntity | 
                      Where-Object { $_.Description -like "*SkyStar*" -or 
                                    $_.Description -like "*TechnoTrend*" -or
                                    $_.Description -like "*Budget*" -or
                                    $_.Description -like "*Pinnacle*" }
        
        foreach ($device in $pciDevices) {
            Write-ColorOutput "PCI: $($device.Description)" "Info"
            $devices += @{
                Type = "PCI"
                Description = $device.Description
                Status = $device.Status
                DeviceID = $device.DeviceID
            }
        }
    }
    catch {
        Write-ColorOutput "Error checking PCI devices: $_" "Warning"
    }
    
    if ($devices.Count -eq 0) {
        Write-ColorOutput "No DVB hardware detected" "Warning"
    }
    
    return $devices
}

function Get-SystemInfo {
    Write-ColorOutput "`n=== System Information ===" "Header"
    
    $os = Get-CimInstance -ClassName Win32_OperatingSystem
    $computer = Get-CimInstance -ClassName Win32_ComputerSystem
    
    $info = @{
        OS = "$($os.Caption) $($os.Version)"
        Architecture = $os.OSArchitecture
        Computer = $computer.Model
        TotalMemory = [math]::Round($computer.TotalPhysicalMemory / 1GB, 2)
        PowerShellVersion = $PSVersionTable.PSVersion.ToString()
    }
    
    Write-ColorOutput "OS: $($info.OS) ($($info.Architecture))" "Info"
    Write-ColorOutput "Computer: $($info.Computer)" "Info"
    Write-ColorOutput "Memory: $($info.TotalMemory) GB" "Info"
    Write-ColorOutput "PowerShell: $($info.PowerShellVersion)" "Info"
    
    return $info
}

function Test-InterfaceModules {
    param([string]$InterfacePath)
    
    Write-ColorOutput "`n=== Interface Module Analysis ===" "Header"
    
    $modules = @("Dev_DvbWorld.int", "Dev_Pinnacle.int", "Dev_SkyStar2.int", "Dev_TTBudget.int")
    $results = @()
    
    foreach ($module in $modules) {
        $fullPath = Join-Path $InterfacePath $module
        Write-ColorOutput "`nAnalyzing: $module" "Info"
        
        $info = Get-InterfaceModuleInfo $fullPath
        if ($info) {
            Write-ColorOutput "  Size: $($info.Size) bytes" "Info"
            Write-ColorOutput "  Technology: $($info.Technology)" "Info"
            Write-ColorOutput "  DirectShow: $($info.HasDirectShow)" "Info"
            Write-ColorOutput "  COM: $($info.HasCOM)" "Info"
            if ($info.Version) {
                Write-ColorOutput "  Version: $($info.Version)" "Info"
            }
            
            $results += $info
        }
        else {
            Write-ColorOutput "  ✗ Module not found or corrupted" "Error"
        }
    }
    
    return $results
}

function Export-AnalysisReport {
    param($SystemInfo, $Dependencies, $Hardware, $Modules, [string]$OutputPath)
    
    $report = @{
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        SystemInfo = $SystemInfo
        Dependencies = $Dependencies
        Hardware = $Hardware
        Modules = $Modules
    }
    
    $jsonReport = $report | ConvertTo-Json -Depth 10
    $reportPath = Join-Path $OutputPath "AltDVB_Analysis_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    
    try {
        $jsonReport | Out-File -FilePath $reportPath -Encoding UTF8
        Write-ColorOutput "`nReport exported to: $reportPath" "Success"
    }
    catch {
        Write-ColorOutput "Error exporting report: $_" "Error"
    }
}

# Main execution
Write-ColorOutput "AltDVB Interface Analysis Tool" "Header"
Write-ColorOutput "===============================" "Header"

# Get system information
$systemInfo = Get-SystemInfo

# Test dependencies
$dependencies = Test-Dependencies $AltDVBPath

# Detect hardware
$hardware = Test-Hardware

# Analyze interface modules
$modules = Test-InterfaceModules $InterfacePath

# Summary
Write-ColorOutput "`n=== Analysis Summary ===" "Header"
$workingModules = ($modules | Where-Object { $_ -ne $null }).Count
$totalModules = 4
$workingDeps = ($dependencies | Where-Object { $_.Exists }).Count
$totalDeps = $dependencies.Count

Write-ColorOutput "Interface Modules: $workingModules/$totalModules working" $(if($workingModules -eq $totalModules){"Success"}else{"Warning"})
Write-ColorOutput "Dependencies: $workingDeps/$totalDeps found" $(if($workingDeps -eq $totalDeps){"Success"}else{"Warning"})
Write-ColorOutput "Hardware Devices: $($hardware.Count) detected" $(if($hardware.Count -gt 0){"Success"}else{"Warning"})

# Export report if requested
if ($ExportReport) {
    Export-AnalysisReport $systemInfo $dependencies $hardware $modules $InterfacePath
}

Write-ColorOutput "`nAnalysis complete!" "Success"
