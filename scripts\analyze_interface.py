#!/usr/bin/env python3
"""
AltDVB Interface Binary Analysis Tool
Analyzes .int files to extract technical information and identify potential issues
"""

import struct
import sys
import os
import json
import re
from datetime import datetime
from pathlib import Path

class InterfaceAnalyzer:
    def __init__(self, filename):
        self.filename = filename
        self.data = None
        self.analysis = {
            'filename': filename,
            'timestamp': datetime.now().isoformat(),
            'file_info': {},
            'pe_info': {},
            'strings': [],
            'imports': [],
            'exports': [],
            'technology': 'Unknown',
            'issues': []
        }
        
    def load_file(self):
        """Load the binary file"""
        try:
            with open(self.filename, 'rb') as f:
                self.data = f.read()
            
            # Basic file info
            stat = os.stat(self.filename)
            self.analysis['file_info'] = {
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
            }
            return True
        except Exception as e:
            self.analysis['issues'].append(f"Failed to load file: {e}")
            return False
    
    def analyze_pe_header(self):
        """Analyze PE header structure"""
        if len(self.data) < 64:
            self.analysis['issues'].append("File too small to be a valid PE")
            return False
        
        # Check DOS header
        if self.data[:2] != b'MZ':
            self.analysis['issues'].append("Invalid DOS signature")
            return False
        
        # Get PE header offset
        pe_offset = struct.unpack('<L', self.data[60:64])[0]
        
        if pe_offset >= len(self.data) - 4:
            self.analysis['issues'].append("Invalid PE offset")
            return False
        
        # Check PE signature
        if self.data[pe_offset:pe_offset+4] != b'PE\x00\x00':
            self.analysis['issues'].append("Invalid PE signature")
            return False
        
        # Read COFF header
        coff_start = pe_offset + 4
        if coff_start + 20 > len(self.data):
            self.analysis['issues'].append("Truncated COFF header")
            return False
        
        coff_header = struct.unpack('<HHLLLLHH', self.data[coff_start:coff_start+20])
        
        self.analysis['pe_info'] = {
            'machine': f"0x{coff_header[0]:04x}",
            'num_sections': coff_header[1],
            'timestamp': coff_header[2],
            'timestamp_readable': datetime.fromtimestamp(coff_header[2]).isoformat() if coff_header[2] > 0 else "Invalid",
            'symbol_table_offset': coff_header[3],
            'num_symbols': coff_header[4],
            'optional_header_size': coff_header[6],
            'characteristics': f"0x{coff_header[7]:04x}"
        }
        
        # Analyze optional header if present
        if coff_header[6] > 0:
            opt_start = coff_start + 20
            if opt_start + 28 <= len(self.data):
                opt_header = struct.unpack('<HBBLLLLL', self.data[opt_start:opt_start+28])
                self.analysis['pe_info']['entry_point'] = f"0x{opt_header[4]:08x}"
                self.analysis['pe_info']['image_base'] = f"0x{opt_header[5]:08x}"
        
        return True
    
    def extract_strings(self, min_length=4, max_strings=200):
        """Extract printable strings from binary"""
        strings = []
        current_string = ""
        
        for byte in self.data:
            if 32 <= byte <= 126:  # Printable ASCII
                current_string += chr(byte)
            else:
                if len(current_string) >= min_length:
                    strings.append(current_string)
                current_string = ""
        
        if len(current_string) >= min_length:
            strings.append(current_string)
        
        # Filter and categorize strings
        interesting_strings = []
        keywords = [
            'dvb', 'interface', 'device', 'driver', 'error', 'init', 'tune', 
            'signal', 'diseqc', 'satellite', 'cable', 'terrestrial', 'usb',
            'pci', 'filter', 'pin', 'media', 'stream', 'buffer', 'channel'
        ]
        
        for s in strings[:max_strings]:
            if any(keyword in s.lower() for keyword in keywords):
                interesting_strings.append(s)
        
        self.analysis['strings'] = interesting_strings[:50]  # Limit output
        
        # Detect technology based on strings
        self.detect_technology(strings)
        
        return len(strings)
    
    def detect_technology(self, strings):
        """Detect the technology stack used"""
        all_strings = ' '.join(strings).lower()
        
        if any(term in all_strings for term in ['tobject', 'tinterfacedobject', 'delphi', 'pascal']):
            self.analysis['technology'] = 'Delphi/Pascal'
        elif any(term in all_strings for term in ['msvcr', 'visual c++', 'crt']):
            self.analysis['technology'] = 'Visual C++'
        elif any(term in all_strings for term in ['.net', 'mscoree', 'clr']):
            self.analysis['technology'] = '.NET'
        elif 'gcc' in all_strings:
            self.analysis['technology'] = 'GCC/MinGW'
        
        # Check for specific frameworks
        frameworks = []
        if any(term in all_strings for term in ['ibasefilter', 'ipin', 'directshow']):
            frameworks.append('DirectShow')
        if any(term in all_strings for term in ['iunknown', 'queryinterface', 'com']):
            frameworks.append('COM')
        if any(term in all_strings for term in ['vcl', 'visual component library']):
            frameworks.append('VCL')
        
        if frameworks:
            self.analysis['frameworks'] = frameworks
    
    def find_imports(self):
        """Find imported DLLs and functions"""
        # This is a simplified import parser
        # Look for common DLL names in strings
        dll_pattern = re.compile(r'([a-zA-Z0-9_]+\.dll)', re.IGNORECASE)
        api_pattern = re.compile(r'([A-Z][a-zA-Z0-9_]*[A-Z][a-zA-Z0-9_]*)', re.IGNORECASE)
        
        all_text = self.data.decode('ascii', errors='ignore')
        
        dlls = set(dll_pattern.findall(all_text))
        apis = set(api_pattern.findall(all_text))
        
        # Filter to likely API names (length > 5, contains uppercase)
        likely_apis = [api for api in apis if len(api) > 5 and any(c.isupper() for c in api)]
        
        self.analysis['imports'] = {
            'dlls': sorted(list(dlls))[:20],  # Limit output
            'functions': sorted(likely_apis)[:30]  # Limit output
        }
    
    def security_analysis(self):
        """Basic security analysis"""
        issues = []
        
        # Check for potentially dangerous functions
        dangerous_funcs = [
            'strcpy', 'strcat', 'sprintf', 'gets', 'scanf',
            'system', 'exec', 'popen', 'CreateProcess'
        ]
        
        all_text = self.data.decode('ascii', errors='ignore').lower()
        
        for func in dangerous_funcs:
            if func.lower() in all_text:
                issues.append(f"Potentially unsafe function: {func}")
        
        # Check file characteristics
        if 'characteristics' in self.analysis['pe_info']:
            chars = int(self.analysis['pe_info']['characteristics'], 16)
            
            # Check for ASLR support (0x0040)
            if not (chars & 0x0040):
                issues.append("ASLR not enabled")
            
            # Check for DEP support (would need more detailed analysis)
            # This is simplified
        
        if issues:
            self.analysis['security_issues'] = issues
    
    def analyze(self):
        """Run complete analysis"""
        print(f"Analyzing: {self.filename}")
        
        if not self.load_file():
            return False
        
        print("  Analyzing PE header...")
        self.analyze_pe_header()
        
        print("  Extracting strings...")
        string_count = self.extract_strings()
        print(f"    Found {string_count} strings")
        
        print("  Analyzing imports...")
        self.find_imports()
        
        print("  Running security analysis...")
        self.security_analysis()
        
        return True
    
    def save_report(self, output_file=None):
        """Save analysis report to JSON file"""
        if output_file is None:
            base_name = Path(self.filename).stem
            output_file = f"{base_name}_analysis.json"
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(self.analysis, f, indent=2, ensure_ascii=False)
            print(f"Report saved to: {output_file}")
            return True
        except Exception as e:
            print(f"Error saving report: {e}")
            return False
    
    def print_summary(self):
        """Print analysis summary"""
        print("\n" + "="*50)
        print("ANALYSIS SUMMARY")
        print("="*50)
        
        print(f"File: {self.filename}")
        print(f"Size: {self.analysis['file_info'].get('size', 'Unknown')} bytes")
        print(f"Technology: {self.analysis['technology']}")
        
        if 'frameworks' in self.analysis:
            print(f"Frameworks: {', '.join(self.analysis['frameworks'])}")
        
        if self.analysis['pe_info']:
            print(f"PE Timestamp: {self.analysis['pe_info'].get('timestamp_readable', 'Unknown')}")
            print(f"Sections: {self.analysis['pe_info'].get('num_sections', 'Unknown')}")
        
        if self.analysis['imports']['dlls']:
            print(f"Key DLLs: {', '.join(self.analysis['imports']['dlls'][:5])}")
        
        if self.analysis['issues']:
            print("\nISSUES FOUND:")
            for issue in self.analysis['issues']:
                print(f"  ⚠ {issue}")
        
        if 'security_issues' in self.analysis:
            print("\nSECURITY CONCERNS:")
            for issue in self.analysis['security_issues']:
                print(f"  🔒 {issue}")
        
        print("\nInteresting strings (sample):")
        for string in self.analysis['strings'][:10]:
            print(f"  {string}")

def main():
    if len(sys.argv) < 2:
        print("Usage: python analyze_interface.py <interface_file.int> [output.json]")
        print("\nExample:")
        print("  python analyze_interface.py Dev_DvbWorld.int")
        print("  python analyze_interface.py Dev_DvbWorld.int dvbworld_report.json")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    if not os.path.exists(input_file):
        print(f"Error: File not found: {input_file}")
        sys.exit(1)
    
    analyzer = InterfaceAnalyzer(input_file)
    
    if analyzer.analyze():
        analyzer.print_summary()
        analyzer.save_report(output_file)
        print("\nAnalysis completed successfully!")
    else:
        print("Analysis failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
