@echo off
setlocal enabledelayedexpansion

:: AltDVB Interface Setup Checker
:: Comprehensive batch script to verify AltDVB installation and interface modules

title AltDVB Interface Setup Checker v1.0
color 0F

echo.
echo =====================================
echo  AltDVB Interface Setup Checker v1.0
echo =====================================
echo.

:: Configuration
set "ALTDVB_PATH=C:\AltDVB"
set "CURRENT_DIR=%~dp0"
set "LOG_FILE=%CURRENT_DIR%setup_check.log"
set "ERROR_COUNT=0"
set "WARNING_COUNT=0"

:: Initialize log file
echo AltDVB Setup Check - %date% %time% > "%LOG_FILE%"
echo ================================================ >> "%LOG_FILE%"

:: Function to log messages
goto :main

:log_message
echo %~1 >> "%LOG_FILE%"
goto :eof

:print_status
if "%~2"=="OK" (
    echo [OK] %~1
    call :log_message "[OK] %~1"
) else if "%~2"=="WARN" (
    echo [WARN] %~1
    call :log_message "[WARN] %~1"
    set /a WARNING_COUNT+=1
) else if "%~2"=="ERROR" (
    echo [ERROR] %~1
    call :log_message "[ERROR] %~1"
    set /a ERROR_COUNT+=1
) else (
    echo [INFO] %~1
    call :log_message "[INFO] %~1"
)
goto :eof

:check_file
if exist "%~1" (
    call :print_status "%~2 found" "OK"
    
    :: Get file size
    for %%F in ("%~1") do set "filesize=%%~zF"
    call :log_message "    Size: !filesize! bytes"
    
    :: Get file date
    for %%F in ("%~1") do set "filedate=%%~tF"
    call :log_message "    Date: !filedate!"
    
    goto :eof
) else (
    call :print_status "%~2 not found" "ERROR"
    goto :eof
)

:check_file_warn
if exist "%~1" (
    call :print_status "%~2 found" "OK"
    goto :eof
) else (
    call :print_status "%~2 not found" "WARN"
    goto :eof
)

:check_directory
if exist "%~1" (
    call :print_status "Directory %~2 exists" "OK"
    goto :eof
) else (
    call :print_status "Directory %~2 missing" "ERROR"
    goto :eof
)

:check_dll_version
if exist "%~1" (
    :: Use PowerShell to get version info
    for /f "tokens=*" %%i in ('powershell -command "(Get-Item '%~1').VersionInfo.FileVersion"') do set "version=%%i"
    if "!version!"=="" set "version=Unknown"
    call :print_status "%~2 found (v!version!)" "OK"
    call :log_message "    Version: !version!"
    
    :: Check minimum version if specified
    if not "%~3"=="" (
        :: Simple version comparison (assumes format x.y.z.w)
        call :log_message "    Required: %~3 or higher"
        :: Note: Full version comparison would require more complex logic
    )
    goto :eof
) else (
    call :print_status "%~2 not found" "WARN"
    goto :eof
)

:main

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    call :print_status "Running with administrator privileges" "OK"
) else (
    call :print_status "Not running as administrator (some checks may fail)" "WARN"
)

echo.
echo Checking AltDVB installation...
echo ================================

:: Check base AltDVB installation
call :check_directory "%ALTDVB_PATH%" "AltDVB base"
call :check_file "%ALTDVB_PATH%\AltDVB.exe" "AltDVB executable"

:: Check interface directory
call :check_directory "%ALTDVB_PATH%\Interfaces" "Interfaces directory"

:: Check device directory
call :check_directory "%ALTDVB_PATH%\Devices" "Devices directory"

echo.
echo Checking interface modules...
echo =============================

:: Check each interface module
call :check_file_warn "%ALTDVB_PATH%\Interfaces\Dev_DvbWorld.int" "DvbWorld interface"
call :check_file_warn "%ALTDVB_PATH%\Interfaces\Dev_Pinnacle.int" "Pinnacle interface"
call :check_file_warn "%ALTDVB_PATH%\Interfaces\Dev_SkyStar2.int" "SkyStar2 interface"
call :check_file_warn "%ALTDVB_PATH%\Interfaces\Dev_TTBudget.int" "TT Budget interface"

:: Check current directory for interface modules
echo.
echo Checking current directory for interface modules...
echo ==================================================

call :check_file_warn "%CURRENT_DIR%Dev_DvbWorld.int" "DvbWorld interface (current dir)"
call :check_file_warn "%CURRENT_DIR%Dev_Pinnacle.int" "Pinnacle interface (current dir)"
call :check_file_warn "%CURRENT_DIR%Dev_SkyStar2.int" "SkyStar2 interface (current dir)"
call :check_file_warn "%CURRENT_DIR%Dev_TTBudget.int" "TT Budget interface (current dir)"

echo.
echo Checking dependencies...
echo ========================

:: Check DvbWorld dependencies
call :check_dll_version "%ALTDVB_PATH%\DwUsbApi.dll" "DwUsbApi.dll (DvbWorld support)"

:: Check TT Budget dependencies
call :check_dll_version "%ALTDVB_PATH%\Devices\ttlcdacc.dll" "ttlcdacc.dll (TT Budget support)" "2.19.8.1"

echo.
echo Checking system information...
echo ==============================

:: Get Windows version
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
call :print_status "Windows version: %VERSION%" "INFO"

:: Check if 64-bit system
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    call :print_status "64-bit system detected" "INFO"
) else (
    call :print_status "32-bit system detected" "INFO"
)

:: Check available disk space
for /f "tokens=3" %%a in ('dir /-c "%ALTDVB_PATH%" 2^>nul ^| find "bytes free"') do set "freespace=%%a"
if defined freespace (
    call :print_status "Free disk space: %freespace% bytes" "INFO"
) else (
    call :print_status "Could not determine free disk space" "WARN"
)

echo.
echo Checking hardware...
echo ===================

:: Use PowerShell to check for DVB hardware
powershell -command "Get-PnpDevice | Where-Object {$_.FriendlyName -like '*DVB*' -or $_.FriendlyName -like '*PCTV*' -or $_.FriendlyName -like '*SkyStar*' -or $_.FriendlyName -like '*TechnoTrend*'} | ForEach-Object {Write-Host '[INFO] DVB Device:' $_.FriendlyName '(' $_.Status ')'}" 2>nul

if %errorlevel% neq 0 (
    call :print_status "PowerShell hardware check failed" "WARN"
    call :print_status "Manual hardware check recommended" "INFO"
)

echo.
echo Checking Windows services...
echo ============================

:: Check for relevant Windows services
sc query "PlugPlay" | find "RUNNING" >nul
if %errorlevel% == 0 (
    call :print_status "Plug and Play service running" "OK"
) else (
    call :print_status "Plug and Play service not running" "ERROR"
)

echo.
echo =====================================
echo  SUMMARY
echo =====================================

call :log_message ""
call :log_message "SUMMARY:"
call :log_message "Errors: %ERROR_COUNT%"
call :log_message "Warnings: %WARNING_COUNT%"

if %ERROR_COUNT% == 0 (
    if %WARNING_COUNT% == 0 (
        echo Status: ALL CHECKS PASSED
        call :log_message "Status: ALL CHECKS PASSED"
        color 0A
    ) else (
        echo Status: PASSED WITH WARNINGS (%WARNING_COUNT% warnings)
        call :log_message "Status: PASSED WITH WARNINGS"
        color 0E
    )
) else (
    echo Status: FAILED (%ERROR_COUNT% errors, %WARNING_COUNT% warnings)
    call :log_message "Status: FAILED"
    color 0C
)

echo.
echo Errors: %ERROR_COUNT%
echo Warnings: %WARNING_COUNT%
echo.
echo Detailed log saved to: %LOG_FILE%
echo.

:: Recommendations based on findings
echo RECOMMENDATIONS:
echo ================

if %ERROR_COUNT% gtr 0 (
    echo - Fix critical errors before using AltDVB
    echo - Ensure all required files are in correct locations
)

if %WARNING_COUNT% gtr 0 (
    echo - Review warnings for potential issues
    echo - Install missing optional components if needed
)

echo - Run this check after making any changes
echo - Check hardware connections if no devices detected
echo - Ensure drivers are installed for your DVB hardware

echo.
echo Press any key to exit...
pause >nul

endlocal
